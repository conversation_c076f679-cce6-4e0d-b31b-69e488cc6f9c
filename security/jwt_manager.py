"""
Secure JWT Manager Implementation

This module provides a secure, stateless JWT authentication system following
industry best practices and security standards.
"""

import os
import secrets
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Any, Union
from uuid import UUID
import logging

from jose import jwt, JW<PERSON>rror
from jose.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from pydantic import BaseModel, Field

from config import settings
from models.role import Permission

logger = logging.getLogger(__name__)

# JWT Configuration Constants
JWT_ALGORITHM = "HS256"
JWT_ISSUER = "assivy-api"
JWT_AUDIENCE = "assivy-client"
ACCESS_TOKEN_EXPIRE_MINUTES = 120
REFRESH_TOKEN_EXPIRE_DAYS = 30

# Allowed algorithms to prevent algorithm confusion attacks
ALLOWED_ALGORITHMS = [JWT_ALGORITHM]

class JWTClaims(BaseModel):
    """Standard JWT claims structure"""
    sub: str = Field(..., description="Subject (user ID)")
    email: str = Field(..., description="User email")
    tid: str = Field(..., description="Tenant ID")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    role: str = Field(..., description="User role name")
    iss: str = Field(default=JWT_ISSUER, description="Issuer")
    aud: str = Field(default=JWT_AUDIENCE, description="Audience")
    iat: datetime = Field(..., description="Issued at")
    exp: datetime = Field(..., description="Expiration time")
    jti: Optional[str] = Field(None, description="JWT ID for tracking")
    
    class Config:
        json_encoders = {
            datetime: lambda v: int(v.timestamp())
        }

class JWTManager:
    """
    Secure JWT Manager implementing stateless authentication
    
    Features:
    - Cryptographic signature verification only (no database lookups)
    - Algorithm confusion protection
    - Proper claims validation
    - Secure key management
    - Token lifecycle management
    """
    
    def __init__(self):
        self.secret_key = self._get_secure_secret_key()
        self.algorithm = JWT_ALGORITHM
        self.issuer = JWT_ISSUER
        self.audience = JWT_AUDIENCE
        
    def _get_secure_secret_key(self) -> str:
        """
        Get or generate a secure secret key
        
        Returns:
            str: Secure secret key for JWT signing
            
        Raises:
            RuntimeError: If no secure key is available
        """
        # Try environment variable first
        secret = os.getenv("JWT_SECRET_KEY")
        if secret and len(secret) >= 32:
            return secret
            
        # Try settings (but validate it's not the default)
        if (hasattr(settings, 'secret_key') and 
            settings.secret_key != "YOUR-SECRET-KEY-HERE" and 
            len(settings.secret_key) >= 32):
            return settings.secret_key
            
        # In development, generate a secure key
        if os.getenv("ENVIRONMENT", "production").lower() in ["development", "dev", "local"]:
            logger.warning("Generating temporary JWT secret key for development")
            return secrets.token_urlsafe(64)
            
        # In production, require explicit configuration
        raise RuntimeError(
            "No secure JWT secret key configured. Set JWT_SECRET_KEY environment variable "
            "with at least 32 characters or configure settings.secret_key"
        )
    
    def create_access_token(
        self,
        user_id: Union[str, UUID],
        email: str,
        tenant_id: Union[str, UUID],
        permissions: List[str],
        role: str,
        expires_delta: Optional[timedelta] = None,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a secure access token with all required claims
        
        Args:
            user_id: User identifier
            email: User email address
            tenant_id: Tenant identifier
            permissions: List of user permissions
            role: User role name
            expires_delta: Custom expiration time
            additional_claims: Additional claims to include
            
        Returns:
            str: Signed JWT token
        """
        now = datetime.now(timezone.utc)
        
        if expires_delta:
            expire = now + expires_delta
        else:
            expire = now + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        # Create standard claims
        claims = JWTClaims(
            sub=str(user_id),
            email=email,
            tid=str(tenant_id),
            permissions=permissions,
            role=role,
            iss=self.issuer,
            aud=self.audience,
            iat=now,
            exp=expire,
            jti=secrets.token_urlsafe(16)  # Unique token ID
        )
        
        # Convert to dict for JWT encoding
        payload = claims.dict()
        
        # Add any additional claims
        if additional_claims:
            payload.update(additional_claims)
        
        # Convert datetime objects to timestamps
        payload["iat"] = int(now.timestamp())
        payload["exp"] = int(expire.timestamp())
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            logger.debug(f"Created access token for user {user_id}")
            return token
        except Exception as e:
            logger.error(f"Failed to create access token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create access token"
            )
    
    def create_refresh_token(
        self,
        user_id: Union[str, UUID],
        email: str,
        tenant_id: Union[str, UUID],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a refresh token with minimal claims
        
        Args:
            user_id: User identifier
            email: User email address
            tenant_id: Tenant identifier
            expires_delta: Custom expiration time
            
        Returns:
            str: Signed JWT refresh token
        """
        now = datetime.now(timezone.utc)
        
        if expires_delta:
            expire = now + expires_delta
        else:
            expire = now + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        
        payload = {
            "sub": str(user_id),
            "email": email,
            "tid": str(tenant_id),
            "type": "refresh",
            "iss": self.issuer,
            "aud": self.audience,
            "iat": int(now.timestamp()),
            "exp": int(expire.timestamp()),
            "jti": secrets.token_urlsafe(16)
        }
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            logger.debug(f"Created refresh token for user {user_id}")
            return token
        except Exception as e:
            logger.error(f"Failed to create refresh token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create refresh token"
            )
    
    def verify_token(self, token: str, token_type: str = "access") -> JWTClaims:
        """
        Verify and decode a JWT token using ONLY cryptographic validation
        
        This method is completely stateless and does NOT perform any database lookups.
        All user information and permissions are contained within the token itself.
        
        Args:
            token: JWT token to verify
            token_type: Type of token ("access" or "refresh")
            
        Returns:
            JWTClaims: Decoded and validated token claims
            
        Raises:
            HTTPException: If token is invalid, expired, or malformed
        """
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No token provided",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        try:
            # Decode and verify token with strict algorithm enforcement
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=ALLOWED_ALGORITHMS,  # Prevent algorithm confusion
                issuer=self.issuer,  # Validate issuer
                audience=self.audience,  # Validate audience
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_iss": True,
                    "verify_aud": True,
                    "require_exp": True,
                    "require_iat": True,
                    "require_iss": True,
                    "require_aud": True
                }
            )
            
            # Validate token type for refresh tokens
            if token_type == "refresh":
                if payload.get("type") != "refresh":
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid token type"
                    )
                # Return minimal claims for refresh tokens
                return JWTClaims(
                    sub=payload["sub"],
                    email=payload["email"],
                    tid=payload["tid"],
                    permissions=[],
                    role="",
                    iss=payload["iss"],
                    aud=payload["aud"],
                    iat=datetime.fromtimestamp(payload["iat"], tz=timezone.utc),
                    exp=datetime.fromtimestamp(payload["exp"], tz=timezone.utc),
                    jti=payload.get("jti")
                )
            
            # Validate required claims for access tokens
            required_claims = ["sub", "email", "tid", "permissions", "role"]
            missing_claims = [claim for claim in required_claims if claim not in payload]
            if missing_claims:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Token missing required claims: {missing_claims}"
                )
            
            # Create and return validated claims
            return JWTClaims(
                sub=payload["sub"],
                email=payload["email"],
                tid=payload["tid"],
                permissions=payload.get("permissions", []),
                role=payload["role"],
                iss=payload["iss"],
                aud=payload["aud"],
                iat=datetime.fromtimestamp(payload["iat"], tz=timezone.utc),
                exp=datetime.fromtimestamp(payload["exp"], tz=timezone.utc),
                jti=payload.get("jti")
            )
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"}
            )
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token verification failed",
                headers={"WWW-Authenticate": "Bearer"}
            )
    
    def extract_token_from_header(self, authorization: Optional[str]) -> Optional[str]:
        """
        Extract JWT token from Authorization header
        
        Args:
            authorization: Authorization header value
            
        Returns:
            str: Extracted token or None if not found
        """
        if not authorization:
            return None
            
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                return None
            return token
        except ValueError:
            return None

# Global JWT manager instance
jwt_manager = JWTManager()
